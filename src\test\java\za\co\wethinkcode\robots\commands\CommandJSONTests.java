package za.co.wethinkcode.robots.commands;

import org.junit.Test;

import static org.junit.jupiter.api.Assertions.*;





public class CommandJSONTests {

    private static class CommandExpectation {
        final Class<?> expectedClass;
        final String expectedCommand;
        final String expectedRobot;
        final String[] expectedArgs;

        public CommandExpectation(Class<?> expectedClass, String expectedCommand, String expectedRobot, String... expectedArgs) {
            this.expectedClass = expectedClass;
            this.expectedCommand = expectedCommand;
            this.expectedRobot = expectedRobot;
            this.expectedArgs = expectedArgs;
        }
    }

    private void assertSimpleCommand(String input, String robotName, CommandExpectation expected) {
        Command command = Command.fromInput(input, robotName);
        assertCommandMatches(command, expected);
    }

    private void assertCommandMatches(Command command, CommandExpectation expected) {
        assertEquals(expected.expectedClass, command.getClass(), "Unexpected command class.");
        assertEquals(expected.expectedCommand, command.commandName(), "Unexpected command name.");
        assertEquals(expected.expectedRobot, command.robot.getName(), "Unexpected robot name.");
        assertArrayEquals(expected.expectedArgs, command.arguments, "Unexpected arguments.");
    }

    @Test
    public void testMovementCommands() {
        assertSimpleCommand("back 1", "hal", new CommandExpectation(MoveCommand.class, "back", "hal", "back", "hal", "1"));
        assertSimpleCommand("back hal 1", null, new CommandExpectation(MoveCommand.class, "back", "hal", "back", "hal", "1"));
        assertSimpleCommand("forward 1", "hal", new CommandExpectation(MoveCommand.class, "forward", "hal", "forward", "hal", "1"));
        assertSimpleCommand("forward hal 1", null, new CommandExpectation(MoveCommand.class, "forward", "hal", "forward", "hal", "1"));
    }

    @Test
    public void testTurnCommand() {
        assertSimpleCommand("turn right", "hal", new CommandExpectation(TurnCommand.class, "turn", "hal", "right"));
        assertSimpleCommand("turn hal right", null, new CommandExpectation(TurnCommand.class, "turn", "hal", "right"));
        assertSimpleCommand("turn left", "hal", new CommandExpectation(TurnCommand.class, "turn", "hal", "left"));
        assertSimpleCommand("turn hal left", null, new CommandExpectation(TurnCommand.class, "turn", "hal", "left"));
    }

    @Test
    public void testStateCommand() {
        assertSimpleCommand("state", "hal", new CommandExpectation(StateCommand.class, "state", "hal"));
        assertSimpleCommand("state hal", null, new CommandExpectation(StateCommand.class, "state", "hal"));
    }

    @Test
    public void testLookCommand() {
        assertSimpleCommand("look", "hal", new CommandExpectation(LookCommand.class, "look", "hal"));
        assertSimpleCommand("look hal", null, new CommandExpectation(LookCommand.class, "look", "hal"));
    }

    @Test
    public void testOrientationCommand() {
        assertSimpleCommand("orientation", "hal", new CommandExpectation(OrientationCommand.class, "orientation", "hal"));
        assertSimpleCommand("orientation hal", null, new CommandExpectation(OrientationCommand.class, "orientation", "hal"));
    }

    @Test
    public void testFireCommand() {
        assertSimpleCommand("fire", "hal", new CommandExpectation(FireCommand.class, "fire", "hal"));
        assertSimpleCommand("fire hal", null, new CommandExpectation(FireCommand.class, "fire", "hal"));
    }

    @Test
    public void testReloadCommand() {
        assertSimpleCommand("reload", "hal", new CommandExpectation(ReloadCommand.class, "reload", "hal"));
        assertSimpleCommand("reload hal", null, new CommandExpectation(ReloadCommand.class, "reload", "hal"));
    }

    @Test
    public void testRepairCommand() {
        assertSimpleCommand("repair", "hal", new CommandExpectation(RepairCommand.class, "repair", "hal"));
        assertSimpleCommand("repair hal", null, new CommandExpectation(RepairCommand.class, "repair", "hal"));
    }

    @Test
    public void testOffCommand() {
        assertSimpleCommand("off", "hal", new CommandExpectation(ShutdownCommand.class, "off", "hal"));
        assertSimpleCommand("off hal", null, new CommandExpectation(ShutdownCommand.class, "off", "hal"));
    }

    @Test
    public void testDumpCommand() {
        Command command = Command.fromInput("dump", null);
        assertEquals(DumpCommand.class, command.getClass());
        assertEquals("dump", command.commandName());
    }

    @Test
    public void testLaunchCommand() {
        assertSimpleCommand("launch tank", "hal", new CommandExpectation(LaunchCommand.class, "launch", "hal", "tank"));
        assertSimpleCommand("launch tank hal", null, new CommandExpectation(LaunchCommand.class, "launch", "hal", "tank"));
    }
}

