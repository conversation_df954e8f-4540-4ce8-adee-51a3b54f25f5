# Robot World Script Runner for PowerShell
Write-Host "Robot World Script Runner" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Check if Git Bash is available
$gitBashPath = "C:\Program Files\Git\bin\bash.exe"
if (-not (Test-Path $gitBashPath)) {
    Write-Host "Git Bash not found at $gitBashPath" -ForegroundColor Red
    Write-Host "Please install Git for Windows or update the path in this script" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Available commands:" -ForegroundColor Yellow
Write-Host "1. Compile project"
Write-Host "2. Start reference server"
Write-Host "3. Run tests"
Write-Host "4. Stop servers"
Write-Host "5. Clean project"
Write-Host "6. Package project"
Write-Host ""

$choice = Read-Host "Enter your choice (1-6)"

$currentDir = Get-Location
$bashCommand = ""

switch ($choice) {
    "1" { 
        Write-Host "Compiling project..." -ForegroundColor Cyan
        $bashCommand = "cd '$currentDir' && ./scripts/compile.sh"
    }
    "2" { 
        Write-Host "Starting reference server..." -ForegroundColor Cyan
        $bashCommand = "cd '$currentDir' && ./scripts/reference-server.sh"
    }
    "3" { 
        Write-Host "Running tests..." -ForegroundColor Cyan
        $bashCommand = "cd '$currentDir' && ./scripts/test.sh"
    }
    "4" { 
        Write-Host "Stopping servers..." -ForegroundColor Cyan
        $bashCommand = "cd '$currentDir' && ./scripts/stop.sh"
    }
    "5" { 
        Write-Host "Cleaning project..." -ForegroundColor Cyan
        $bashCommand = "cd '$currentDir' && ./scripts/clean.sh"
    }
    "6" { 
        Write-Host "Packaging project..." -ForegroundColor Cyan
        $bashCommand = "cd '$currentDir' && ./scripts/package.sh"
    }
    default { 
        Write-Host "Invalid choice" -ForegroundColor Red
        exit 1
    }
}

# Execute the bash command
& $gitBashPath -c $bashCommand

Write-Host ""
Write-Host "Script execution completed." -ForegroundColor Green
