@echo off
echo Starting Git Bash with Robot World scripts...
echo.
echo Available options:
echo 1. Run interactive test runner
echo 2. Start reference server only
echo 3. Run tests only
echo 4. Stop servers
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    "C:\Program Files\Git\bin\bash.exe" -c "cd '%CD%' && ./run-tests.sh"
) else if "%choice%"=="2" (
    "C:\Program Files\Git\bin\bash.exe" -c "cd '%CD%' && ./scripts/reference-server.sh"
) else if "%choice%"=="3" (
    "C:\Program Files\Git\bin\bash.exe" -c "cd '%CD%' && ./scripts/test.sh"
) else if "%choice%"=="4" (
    "C:\Program Files\Git\bin\bash.exe" -c "cd '%CD%' && ./scripts/stop.sh"
) else (
    echo Invalid choice
)

pause
