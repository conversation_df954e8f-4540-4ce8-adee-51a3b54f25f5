#!/bin/bash

echo "Robot World Test Runner"
echo "======================"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "Checking prerequisites..."

if ! command_exists java; then
    echo "❌ Java not found. Please install Java."
    exit 1
fi

if ! command_exists mvn; then
    echo "❌ <PERSON>ven not found. Please install Maven."
    exit 1
fi

echo "✅ Java and <PERSON><PERSON> found"

# Make scripts executable
chmod +x scripts/*.sh

echo ""
echo "Available commands:"
echo "1. Compile project: ./scripts/compile.sh"
echo "2. Start reference server: ./scripts/reference-server.sh"
echo "3. Run tests: ./scripts/test.sh"
echo "4. Stop servers: ./scripts/stop.sh"
echo "5. Clean project: ./scripts/clean.sh"
echo "6. Package project: ./scripts/package.sh"
echo ""

# Ask user what they want to do
echo "What would you like to do?"
echo "1) Compile only"
echo "2) Start reference server and run tests"
echo "3) Run tests only (server must be running)"
echo "4) Stop all servers"
echo "5) Clean and compile"
echo "6) Package application"
echo ""
read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        echo "Compiling project..."
        ./scripts/compile.sh
        ;;
    2)
        echo "Starting reference server and running tests..."
        ./scripts/reference-server.sh
        sleep 3
        ./scripts/test.sh
        ;;
    3)
        echo "Running tests..."
        ./scripts/test.sh
        ;;
    4)
        echo "Stopping servers..."
        ./scripts/stop.sh
        ;;
    5)
        echo "Cleaning and compiling..."
        ./scripts/clean.sh
        ./scripts/compile.sh
        ;;
    6)
        echo "Packaging application..."
        ./scripts/package.sh
        ;;
    *)
        echo "Invalid choice. Please run the script again."
        ;;
esac
