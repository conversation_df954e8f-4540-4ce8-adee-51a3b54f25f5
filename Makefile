# Makefile for Robot Worlds

# Variables
PROJECT_NAME = robot-world
VERSION = 0.0.1-SNAPSHOT

# Targets
.PHONY: all compile test reference-server own-server clean package release tag stop

all: compile test

compile:
	@./scripts/compile.sh

test: reference-server
	@./scripts/test.sh

reference-server:
	@./scripts/reference-server.sh

own-server:
	@./scripts/own-server.sh

package:
	@./scripts/package.sh

release: clean
	@./scripts/release.sh

clean:
	@./scripts/clean.sh

tag:
	@./scripts/tag.sh

stop:
	@./scripts/stop.sh
