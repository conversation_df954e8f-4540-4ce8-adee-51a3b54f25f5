# Makefile for Robot Worlds

# Variables
PROJECT_NAME = robot-world
VERSION = 0.0.1-SNAPSHOT
POM_FILE = pom.xml
REFERENCE_SERVER_JAR = ./lib/reference-server-0.1.0.jar
MVN = mvn

# Targets
.PHONY: all compile test reference-server own-server clean package release tag

all: compile test

compile:
	@$(MVN) compile

# Run tests against the reference server
test: reference-server
	@$(MVN) test

# Start the reference server
reference-server:
	@echo "Starting reference server..."
	@java -jar $(REFERENCE_SERVER_JAR) & \
		echo $$! > server.pid; \
		sleep 2; \
		echo "Reference server started."

# Run tests against your own server
own-server:
	@echo "Starting own server..."
	@java -jar target/$(PROJECT_NAME)-$(VERSION).jar & \
		echo $$! > own_server.pid; \
		sleep 2; \
		@$(MVN) test

# Package the application
package:
	@$(MVN) package

# Prepare for release
release: clean
	sed -i 's/-SNAPSHOT//g' $(POM_FILE)
	@$(MVN) package

# Clean up target directory
clean:
	@$(MVN) clean

# Tag the release version in Git
tag:
	git tag release-$(VERSION)
	git push origin --tags

# Stop servers
stop:
	@kill `cat server.pid` && rm server.pid || echo "Reference server not running."
	@kill `cat own_server.pid` && rm own_server.pid || echo "Own server not running."